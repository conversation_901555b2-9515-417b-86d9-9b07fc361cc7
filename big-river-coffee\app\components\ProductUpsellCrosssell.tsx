import {Link} from 'react-router';
import {Money, Image} from '@shopify/hydrogen';
import {AddToCartButton} from './AddToCartButton';
import {useAside} from './Aside';
import type {ProductItemFragment} from 'storefrontapi.generated';

interface UpsellCrosssellProps {
  currentProduct: ProductFragment;
  allProducts?: any[]; // This would come from a loader or context
}

export function ProductUpsellCrosssell({currentProduct, allProducts = []}: UpsellCrosssellProps) {
  const {open} = useAside();

  // Bundle products for upselling
  const bundleProducts = allProducts.filter(product =>
    product.title.toLowerCase().includes('box') ||
    product.title.toLowerCase().includes('bundle') ||
    product.tags?.some((tag: string) => tag.toLowerCase().includes('bundle'))
  );

  // Related coffee products for cross-selling (exclude current product)
  const relatedProducts = allProducts.filter(product =>
    product.id !== currentProduct.id &&
    !product.title.toLowerCase().includes('box') &&
    !product.title.toLowerCase().includes('bundle') &&
    (product.title.toLowerCase().includes('coffee') ||
     product.tags?.some((tag: string) => tag.toLowerCase().includes('coffee')))
  ).slice(0, 6);

  // K-cup products for cross-selling
  const kcupProducts = allProducts.filter(product =>
    product.title.toLowerCase().includes('k-cup') ||
    product.title.toLowerCase().includes('kcup') ||
    product.tags?.some((tag: string) => tag.toLowerCase().includes('k-cup'))
  ).slice(0, 4);

  // Calculate potential savings for bundles
  const currentPrice = currentProduct.priceRange?.minVariantPrice?.amount ?
    parseFloat(currentProduct.priceRange.minVariantPrice.amount) : 0;

  // If no products are loaded, show fallback content
  if (allProducts.length === 0) {
    return (
      <div className="space-y-12">
        {/* Fallback Bundle Upsell */}
        <div className="bg-gradient-to-r from-[#db8027] to-[#c4721f] rounded-2xl p-8 text-white text-center">
          <h2 className="text-3xl font-bold mb-4">🎁 Upgrade Your Coffee Experience</h2>
          <p className="text-xl opacity-90 mb-6">Get more variety and save with our curated bundles</p>
          <Link
            to="/collections/all"
            className="inline-block bg-white text-[#db8027] font-bold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors"
          >
            Explore Our Bundles
          </Link>
        </div>

        {/* Fallback Cross-sell */}
        <div className="bg-[#eeedc1] rounded-2xl p-8 text-center">
          <h2 className="text-3xl font-bold text-[#3a5c5c] mb-4">☕ Discover More Amazing Coffee</h2>
          <p className="text-xl text-[#3a5c5c]/80 mb-6">Explore our full collection of premium coffee products</p>
          <Link
            to="/collections/all"
            className="inline-block bg-[#3a5c5c] text-white font-bold py-3 px-8 rounded-lg hover:bg-[#2d4747] transition-colors"
          >
            Shop All Coffee
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-12">
      {/* Enhanced Upsell Section - Bundle Products */}
      <div className="bg-gradient-to-r from-[#db8027] to-[#c4721f] rounded-2xl p-8 text-white relative overflow-hidden">
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-16 -translate-y-16"></div>
          <div className="absolute bottom-0 right-0 w-24 h-24 bg-white rounded-full translate-x-12 translate-y-12"></div>
          <div className="absolute top-1/2 left-1/3 w-16 h-16 bg-white rounded-full"></div>
        </div>

        <div className="relative z-10">
          <div className="text-center mb-8">
            <div className="inline-flex items-center space-x-2 mb-4">
              <span className="text-4xl">🎁</span>
              <h2 className="text-3xl font-bold">Upgrade Your Coffee Experience</h2>
            </div>
            <p className="text-xl opacity-90 mb-2">Get more variety and save with our curated bundles</p>
            <div className="inline-flex items-center space-x-2 bg-white/20 rounded-full px-4 py-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
              </svg>
              <span className="font-semibold">Save up to 25% with bundles</span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {bundleProducts.length > 0 ? bundleProducts.slice(0, 2).map((product) => (
              <div key={product.id} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="flex items-start space-x-4">
                  {product.featuredImage && (
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        data={product.featuredImage}
                        aspectRatio="1/1"
                        sizes="80px"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg mb-2">{product.title}</h3>
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-2xl font-bold">
                        <Money data={product.priceRange.minVariantPrice} />
                      </div>
                      <div className="bg-green-500 text-white text-sm px-3 py-1 rounded-full font-bold">
                        Best Value
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/products/${product.handle}`}
                        className="flex-1 bg-white text-[#db8027] font-medium py-2 px-4 rounded-lg text-center hover:bg-gray-100 transition-colors"
                      >
                        View Details
                      </Link>
                      <AddToCartButton
                        disabled={!product.availableForSale}
                        onClick={() => open('cart')}
                        lines={
                          product.variants?.nodes?.[0]
                            ? [
                                {
                                  merchandiseId: product.variants.nodes[0].id,
                                  quantity: 1,
                                  selectedVariant: product.variants.nodes[0],
                                },
                              ]
                            : []
                        }
                        className="flex-1 bg-army-600 hover:bg-army-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                      >
                        Add to Cart
                      </AddToCartButton>
                    </div>
                  </div>
                </div>
              </div>
            )) : (
              // Fallback static bundle options when no dynamic bundles are loaded
              <>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <div className="flex items-start space-x-4">
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 bg-white/20 flex items-center justify-center">
                      <span className="text-2xl">📦</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-2">Roasters Box</h3>
                      <div className="flex items-center justify-between mb-4">
                        <div className="text-2xl font-bold">$49.99</div>
                        <div className="bg-green-500 text-white text-sm px-3 py-1 rounded-full font-bold">
                          Best Value
                        </div>
                      </div>
                      <p className="text-sm opacity-90 mb-4">3 different coffee varieties delivered monthly</p>
                      <div className="flex space-x-2">
                        <Link
                          to="/products/roasters-box"
                          className="flex-1 bg-white text-[#db8027] font-medium py-2 px-4 rounded-lg text-center hover:bg-gray-100 transition-colors"
                        >
                          View Details
                        </Link>
                        <Link
                          to="/products/roasters-box"
                          className="flex-1 bg-army-600 hover:bg-army-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-center"
                        >
                          Shop Now
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <div className="flex items-start space-x-4">
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 bg-white/20 flex items-center justify-center">
                      <span className="text-2xl">🎯</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-2">Blend Box</h3>
                      <div className="flex items-center justify-between mb-4">
                        <div className="text-2xl font-bold">$39.99</div>
                        <div className="bg-blue-500 text-white text-sm px-3 py-1 rounded-full font-bold">
                          Popular
                        </div>
                      </div>
                      <p className="text-sm opacity-90 mb-4">Curated selection of our signature blends</p>
                      <div className="flex space-x-2">
                        <Link
                          to="/collections/all"
                          className="flex-1 bg-white text-[#db8027] font-medium py-2 px-4 rounded-lg text-center hover:bg-gray-100 transition-colors"
                        >
                          View Details
                        </Link>
                        <Link
                          to="/collections/all"
                          className="flex-1 bg-army-600 hover:bg-army-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-center"
                        >
                          Shop Now
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Cross-sell Section - Related Coffee Products */}
      {relatedProducts.length > 0 && (
        <div className="bg-[#eeedc1] rounded-2xl p-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-[#3a5c5c] mb-4">☕ You Might Also Love</h2>
            <p className="text-xl text-[#3a5c5c]/80">Discover more amazing flavors from our collection</p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {relatedProducts.map((product) => (
              <div key={product.id} className="bg-white rounded-xl p-4 shadow-lg hover:shadow-xl transition-shadow">
                {product.featuredImage && (
                  <div className="aspect-square rounded-lg overflow-hidden mb-4">
                    <Image
                      data={product.featuredImage}
                      aspectRatio="1/1"
                      sizes="(max-width: 640px) 50vw, (max-width: 1024px) 25vw, 20vw"
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                )}
                <h3 className="font-semibold text-[#3a5c5c] mb-2 line-clamp-2">{product.title}</h3>
                <div className="text-xl font-bold text-[#db8027] mb-4">
                  <Money data={product.priceRange.minVariantPrice} />
                </div>
                <div className="flex space-x-2">
                  <Link
                    to={`/products/${product.handle}`}
                    className="flex-1 bg-[#3a5c5c] text-white font-medium py-2 px-3 rounded-lg text-center text-sm hover:bg-[#2d4747] transition-colors"
                  >
                    View
                  </Link>
                  <AddToCartButton
                    disabled={!product.availableForSale}
                    onClick={() => open('cart')}
                    lines={
                      product.variants?.nodes?.[0]
                        ? [
                            {
                              merchandiseId: product.variants.nodes[0].id,
                              quantity: 1,
                              selectedVariant: product.variants.nodes[0],
                            },
                          ]
                        : []
                    }
                    className="flex-1 bg-[#db8027] hover:bg-[#c4721f] text-white font-medium py-2 px-3 rounded-lg text-sm transition-colors"
                  >
                    Add
                  </AddToCartButton>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Cross-sell Section - K-Cup Products */}
      {kcupProducts.length > 0 && (
        <div className="bg-gradient-to-r from-[#5d8e8e] to-[#4a7373] rounded-2xl p-8 text-white">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">🚀 Quick & Convenient</h2>
            <p className="text-xl opacity-90">Try our K-Cup pods for instant coffee perfection</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {kcupProducts.map((product) => (
              <div key={product.id} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                {product.featuredImage && (
                  <div className="aspect-square rounded-lg overflow-hidden mb-4">
                    <Image
                      data={product.featuredImage}
                      aspectRatio="1/1"
                      sizes="(max-width: 768px) 100vw, 33vw"
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <h3 className="font-semibold text-lg mb-2">{product.title}</h3>
                <div className="text-2xl font-bold mb-4">
                  <Money data={product.priceRange.minVariantPrice} />
                </div>
                <div className="flex space-x-2">
                  <Link
                    to={`/products/${product.handle}`}
                    className="flex-1 bg-white/20 hover:bg-white/30 text-white font-medium py-2 px-4 rounded-lg text-center transition-colors"
                  >
                    View Details
                  </Link>
                  <AddToCartButton
                    disabled={!product.availableForSale}
                    onClick={() => open('cart')}
                    lines={
                      product.variants?.nodes?.[0]
                        ? [
                            {
                              merchandiseId: product.variants.nodes[0].id,
                              quantity: 1,
                              selectedVariant: product.variants.nodes[0],
                            },
                          ]
                        : []
                    }
                    className="flex-1 bg-[#db8027] hover:bg-[#c4721f] text-white font-medium py-2 px-4 rounded-lg transition-colors"
                  >
                    Add to Cart
                  </AddToCartButton>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Call-to-Action for More Products */}
      <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
        <h2 className="text-2xl font-bold text-[#3a5c5c] mb-4">Explore Our Full Collection</h2>
        <p className="text-gray-600 mb-6">Discover all our premium coffee products and find your perfect match</p>
        <Link
          to="/collections/all"
          className="inline-block bg-army-600 hover:bg-army-700 text-white font-medium py-3 px-8 rounded-lg transition-colors"
        >
          Shop All Products
        </Link>
      </div>
    </div>
  );
}
