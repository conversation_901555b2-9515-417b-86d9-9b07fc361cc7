import{w as r}from"./with-props-CE_bzIRz.js";import{j as s}from"./jsx-runtime-CWqDQG74.js";import{u as o,L as n}from"./chunk-D4RADZKF-CZTShXQu.js";import{P as m}from"./PaginatedResourceSection-ClJsfpf4.js";import{I as d}from"./Image-83A1PdZQ.js";import"./index-7gSH2QeO.js";const j=r(function(){const{collections:t}=o();return s.jsxs("div",{className:"collections",children:[s.jsx("h1",{children:"Collections"}),s.jsx(m,{connection:t,resourcesClassName:"collections-grid",children:({node:i,index:a})=>s.jsx(l,{collection:i,index:a},i.id)})]})});function l({collection:e,index:t}){return s.jsxs(n,{className:"collection-item",to:`/collections/${e.handle}`,prefetch:"intent",children:[(e==null?void 0:e.image)&&s.jsx(d,{alt:e.image.altText||e.title,aspectRatio:"1/1",data:e.image,loading:t<3?"eager":void 0,sizes:"(min-width: 45em) 400px, 100vw"}),s.jsx("h5",{children:e.title})]},e.id)}export{j as default};
