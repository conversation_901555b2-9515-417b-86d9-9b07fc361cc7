import{j as t}from"./jsx-runtime-CWqDQG74.js";import{j as s}from"./index-7gSH2QeO.js";function m({analytics:r,children:n,disabled:l,lines:a,onClick:o,className:e}){return t.jsx("div",{className:"w-full block",children:t.jsx(s,{route:"/cart",inputs:{lines:a},action:s.ACTIONS.LinesAdd,children:i=>t.jsxs(t.Fragment,{children:[t.jsx("input",{name:"analytics",type:"hidden",value:JSON.stringify(r)}),t.jsx("button",{type:"submit",onClick:o,disabled:l??i.state!=="idle",className:e||"bg-army-600 hover:bg-army-700 text-white py-2 px-4 rounded-lg font-medium transition-colors duration-200 w-full cursor-pointer",style:e?{width:"100%"}:{width:"100%",display:"block",minWidth:"100%"},children:i.state!=="idle"?t.jsxs("div",{className:"flex items-center justify-center",children:[t.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[t.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),t.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Adding..."]}):n})]})})})}export{m as A};
