const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/GoogleAnalytics-jquwe-Bo.js","assets/index-BYzZDKcn.js","assets/chunk-D4RADZKF-CZTShXQu.js","assets/jsx-runtime-CWqDQG74.js","assets/with-props-CE_bzIRz.js","assets/Aside-D-IBZjP3.js","assets/CartMain-De5XrJ0a.js","assets/variants-QTzM9WEo.js","assets/ProductPrice-DA7E5Y22.js","assets/Money-St0DOtgu.js","assets/Image-83A1PdZQ.js","assets/search-DOeYwaXi.js","assets/GTMLoader-BNOsfSYk.js"])))=>i.map(i=>d[i]);
import{J as U,G as H,$ as z,s as B,_ as T}from"./index-BYzZDKcn.js";import{w as F,a as D}from"./with-props-CE_bzIRz.js";import{j as e}from"./jsx-runtime-CWqDQG74.js";import{d as k,r as u,N as h,A as v,G as V,c as $,L as p,b as W,i as E,H as Y,M as q,I as G,S as K,J,O as Q,h as X,K as Z}from"./chunk-D4RADZKF-CZTShXQu.js";import{u as g,A as y}from"./Aside-D-IBZjP3.js";import{C as ee}from"./CartMain-De5XrJ0a.js";import{g as te,u as b}from"./search-DOeYwaXi.js";import{I as S}from"./Image-83A1PdZQ.js";import{M as se}from"./Money-St0DOtgu.js";const re="/assets/favicon-DZkC1E9c.svg",ie="/assets/reset-BKioPaen.css",ne="/assets/app-DgopwRf6.css",oe="/assets/homepage-7uZSHaEk.css",ae="/assets/tailwind-PrTXUb7P.css";function le({className:t=""}){const s=k(),[i,r]=u.useState(""),[n,l]=u.useState(!1),a=s.state==="submitting",o=s.data;return u.useEffect(()=>{if(o!=null&&o.success){l(!0),r("");const d=setTimeout(()=>{l(!1)},5e3);return()=>clearTimeout(d)}},[o==null?void 0:o.success]),u.useEffect(()=>{n&&i&&l(!1)},[i,n]),e.jsxs("div",{className:`relative bg-gradient-to-r from-army-600 to-army-700 text-white overflow-hidden ${t}`,children:[e.jsx("div",{className:"absolute inset-0 opacity-10",children:e.jsx("div",{className:"absolute inset-0 newsletter-pattern"})}),e.jsx("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"flex flex-col lg:flex-row items-center justify-between gap-6",children:[e.jsxs("div",{className:"flex-1 text-center lg:text-left",children:[e.jsxs("div",{className:"flex items-center justify-center lg:justify-start gap-3 mb-3",children:[e.jsx("div",{className:"bg-orange-500 p-2 rounded-full",children:e.jsxs("svg",{className:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:[e.jsx("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e.jsx("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]})}),e.jsx("h3",{className:"text-xl font-bold",children:"Join Our Coffee Community"})]}),e.jsx("p",{className:"text-2xl font-bold text-orange-300 mb-2",children:"Get 5% Off Your First Order!"}),e.jsx("p",{className:"text-army-100 text-base leading-relaxed max-w-md mx-auto lg:mx-0",children:"Stay updated with new coffee releases, brewing tips, exclusive offers, and behind-the-scenes stories from our mountain roastery."})]}),e.jsxs("div",{className:"flex-shrink-0 w-full lg:w-auto lg:min-w-[400px]",children:[n?e.jsxs("div",{className:"bg-gradient-to-r from-green-500 to-green-600 text-white px-8 py-6 rounded-xl text-center shadow-lg border border-green-400",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-2",children:[e.jsx("div",{className:"bg-white bg-opacity-20 p-2 rounded-full",children:e.jsx("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),e.jsx("span",{className:"font-bold text-lg",children:"Welcome to the Community!"})]}),e.jsx("p",{className:"text-green-100 font-medium",children:"Check your email for your 5% off discount code and brewing tips!"})]}):e.jsx("div",{className:"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 border border-white border-opacity-20 shadow-lg",children:e.jsxs(s.Form,{method:"post",action:"/api/newsletter",className:"space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"email",name:"email",value:i,onChange:d=>r(d.target.value),placeholder:"Enter your email address",required:!0,disabled:a,className:"w-full px-5 py-4 rounded-xl border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-sm transition-all duration-200"}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none",children:e.jsxs("svg",{className:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:[e.jsx("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e.jsx("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]})})]}),e.jsx("button",{type:"submit",disabled:a||!i.trim(),className:"w-full px-8 py-4 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-army-600 shadow-lg transform hover:scale-105 active:scale-95",children:a?e.jsxs("div",{className:"flex items-center justify-center gap-3",children:[e.jsxs("svg",{className:"animate-spin h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),e.jsx("span",{children:"Joining the Community..."})]}):e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("span",{children:"Claim Your 5% Discount"}),e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})]})}),(o==null?void 0:o.error)&&!n&&e.jsx("div",{className:"mt-4 bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-4 rounded-xl shadow-lg border border-red-400",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-white bg-opacity-20 p-1 rounded-full flex-shrink-0",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),e.jsx("span",{className:"font-medium",children:o.error})]})})]})]})})]})}function ce({footer:t,header:s,publicStoreDomain:i}){const r=new Date().getFullYear();return e.jsxs("footer",{className:"text-white mt-auto",style:{backgroundColor:"#3A5C5C"},children:[e.jsx(le,{}),e.jsx("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-12",children:[e.jsxs("div",{className:"col-span-1 md:col-span-2",children:[e.jsx(h,{to:"/",className:"inline-block mb-6",children:e.jsx("img",{src:"/Logo_Official.svg",alt:"Big River Coffee",className:"h-20 w-auto"})}),e.jsx("p",{className:"text-white mb-12 text-base leading-relaxed max-w-md",children:"Premium coffee for adventurers. Ethically sourced from mountain regions, expertly roasted for those who live life to the fullest."}),e.jsxs("div",{className:"flex space-x-4 mt-4",children:[e.jsx("a",{href:"https://www.instagram.com/bigriver.coffee/",target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200","aria-label":"Follow us on Instagram",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})}),e.jsx("a",{href:"https://www.facebook.com/bigriverco/",target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200","aria-label":"Follow us on Facebook",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})}),e.jsx("a",{href:"https://www.tiktok.com/@bigriver.coffee",target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200","aria-label":"Follow us on TikTok",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-.88-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z"})})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-white text-lg font-semibold mb-6",children:"Shop"}),e.jsxs("ul",{className:"space-y-3",children:[e.jsx("li",{children:e.jsx(h,{to:"/collections/all",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Coffee"})}),e.jsx("li",{children:e.jsx(h,{to:"/collections/all",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"K-Cups"})}),e.jsx("li",{children:e.jsx(h,{to:"/collections/all",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Subscriptions"})}),e.jsx("li",{children:e.jsx(h,{to:"/our-story",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Our Story"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-white text-lg font-semibold mb-6",children:"Help"}),e.jsxs("ul",{className:"space-y-3",children:[e.jsx("li",{children:e.jsx(h,{to:"/policies/shipping-policy",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Shipping Info"})}),e.jsx("li",{children:e.jsx(h,{to:"/policies/refund-policy",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Returns"})}),e.jsx("li",{children:e.jsx(h,{to:"/account",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"My Account"})})]})]})]})}),e.jsx("div",{className:"border-t border-white/20",children:e.jsx("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:e.jsxs("div",{className:"md:flex md:items-center md:justify-between",children:[e.jsxs("div",{className:"text-sm text-white",children:["© ",r," Big River Coffee. All rights reserved."]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx(u.Suspense,{children:e.jsx(v,{resolve:t,children:n=>{var l;return(n==null?void 0:n.menu)&&((l=s.shop.primaryDomain)==null?void 0:l.url)&&e.jsx(de,{menu:n.menu,primaryDomainUrl:s.shop.primaryDomain.url,publicStoreDomain:i})}})})})]})})})]})}function de({menu:t,primaryDomainUrl:s,publicStoreDomain:i}){return e.jsx("nav",{role:"navigation",children:e.jsx("ul",{className:"flex flex-wrap gap-6",children:(t||ue).items.map(r=>{if(!r.url)return null;const n=r.url.includes("myshopify.com")||r.url.includes(i)||r.url.includes(s)?new URL(r.url).pathname:r.url,l=!n.startsWith("/");return e.jsx("li",{children:l?e.jsx("a",{href:n,rel:"noopener noreferrer",target:"_blank",className:"text-sm text-white hover:text-orange-300 transition-colors duration-200",children:r.title}):e.jsx(h,{end:!0,prefetch:"intent",to:n,className:"text-sm text-white hover:text-orange-300 transition-colors duration-200",children:r.title})},r.id)})})})}const ue={items:[{id:"gid://shopify/MenuItem/461633060920",resourceId:"gid://shopify/ShopPolicy/23358046264",tags:[],title:"Privacy Policy",type:"SHOP_POLICY",url:"/policies/privacy-policy",items:[]},{id:"gid://shopify/MenuItem/461633093688",resourceId:"gid://shopify/ShopPolicy/23358013496",tags:[],title:"Refund Policy",type:"SHOP_POLICY",url:"/policies/refund-policy",items:[]},{id:"gid://shopify/MenuItem/461633126456",resourceId:"gid://shopify/ShopPolicy/23358111800",tags:[],title:"Shipping Policy",type:"SHOP_POLICY",url:"/policies/shipping-policy",items:[]},{id:"gid://shopify/MenuItem/461633159224",resourceId:"gid://shopify/ShopPolicy/23358079032",tags:[],title:"Terms of Service",type:"SHOP_POLICY",url:"/policies/terms-of-service",items:[]}]};function me({header:t,isLoggedIn:s,cart:i,publicStoreDomain:r}){const{shop:n,menu:l}=t,[a,o]=u.useState(!1),[d,c]=u.useState(!1),[m,x]=u.useState(0);return u.useEffect(()=>{const j=()=>{const w=window.scrollY;o(w>50),w>150?c(!0):c(!1),x(w)};return window.addEventListener("scroll",j),()=>window.removeEventListener("scroll",j)},[m]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"fixed top-0 left-0 right-0 z-50 text-white py-1.5 px-4",style:{backgroundColor:"#7895a4"},children:e.jsx("div",{className:"max-w-7xl mx-auto text-center",children:e.jsx("p",{className:"text-sm font-semibold",children:"☕ First ones on us deal - One Free Coffee Bag with Subscription Purchase ☕"})})}),e.jsx("header",{className:`fixed left-0 right-0 z-40 transition-all duration-300 ${d?"translate-y-0":"-translate-y-full"}`,style:{top:"32px",height:"calc(var(--header-height) - 32px)",backgroundColor:"#3A5C5C",background:"#3A5C5C"},children:e.jsx("div",{className:"max-w-7xl mx-auto relative px-4 sm:px-6 lg:px-8 h-full",children:e.jsxs("div",{className:"flex items-center justify-between h-full",children:[e.jsx("div",{className:"flex justify-start items-center h-full",children:e.jsx(h,{to:"/",prefetch:"intent",end:!0,className:"relative group flex items-center h-full",children:e.jsx("img",{src:"/headerlogo.svg",alt:"Big River Coffee",className:"transition-all duration-300 group-hover:scale-105 h-24 sm:h-32 lg:h-36 w-auto",width:"auto",height:"140"})})}),e.jsx("div",{className:"hidden md:flex items-center space-x-8",children:e.jsx(A,{menu:null,viewport:"desktop",primaryDomainUrl:t.shop.primaryDomain.url,publicStoreDomain:r})}),e.jsx("div",{className:"flex-1 flex justify-end",children:e.jsx(he,{isLoggedIn:s,cart:i})})]})})})]})}function A({menu:t,primaryDomainUrl:s,viewport:i,publicStoreDomain:r}){var l;const{close:n}=g();return i==="mobile"?(console.log("Mobile menu rendering:",{menuExists:!!t,fallbackItems:N.items.length,menuItems:((l=t==null?void 0:t.items)==null?void 0:l.length)||0}),e.jsxs("nav",{className:"header-menu-mobile",role:"navigation",children:[e.jsx(h,{end:!0,onClick:n,prefetch:"intent",to:"/",className:"header-menu-item",children:"Home"}),e.jsx(h,{to:"/rewards",className:"header-menu-item",onClick:n,children:"Rewards"}),N.items.map(a=>{if(!a.url)return null;let o=a.url;if(a.url.includes("myshopify.com")||a.url.includes(r)||a.url.includes(s))try{o=new URL(a.url).pathname}catch{o=a.url}return(o.includes("/products/big-river-k-cups")||a.title&&a.title.toLowerCase().includes("k-cup"))&&(o="/collections/all?section=kcups"),o.includes("#section-subscriptions")&&(o="/collections/all?section=subscriptions"),e.jsx(h,{end:!0,onClick:n,prefetch:"intent",to:o,className:"header-menu-item",children:a.title},a.id)})]})):e.jsx("nav",{className:"flex items-center space-x-8",role:"navigation",children:N.items.map(a=>{if(!a.url)return null;let o=a.url;if(a.url.includes("myshopify.com")||a.url.includes(r)||a.url.includes(s))try{o=new URL(a.url).pathname}catch{o=a.url}return(o.includes("/products/big-river-k-cups")||a.title&&a.title.toLowerCase().includes("k-cup"))&&(o="/collections/all?section=kcups"),o.includes("#section-subscriptions")&&(o="/collections/all?section=subscriptions"),e.jsx(h,{end:!0,prefetch:"intent",to:o,className:({isActive:d})=>`text-white hover:text-orange-300 transition-colors duration-200 font-medium ${d?"text-orange-300":"text-white"}`,children:a.title},a.id)})})}function he({isLoggedIn:t,cart:s}){return e.jsxs("nav",{className:"flex items-center space-x-4",role:"navigation",children:[e.jsxs("div",{className:"hidden md:flex items-center space-x-4",children:[e.jsx(h,{prefetch:"intent",to:"/account",className:"text-white hover:text-orange-300 transition-colors duration-200 font-medium",children:e.jsx(u.Suspense,{fallback:"Sign in",children:e.jsx(v,{resolve:t,errorElement:"Sign in",children:i=>i?"Account":"Sign in"})})}),e.jsx(xe,{})]}),e.jsx(pe,{cart:s}),e.jsx("div",{className:"md:hidden",children:e.jsx(fe,{})})]})}function fe(){const{open:t}=g();return e.jsx("button",{className:"text-white hover:text-orange-300 transition-colors duration-200 p-2",onClick:()=>t("mobile"),"aria-label":"Open mobile menu",style:{color:"white"},children:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}function xe(){const{open:t}=g();return e.jsx("button",{className:"text-white hover:text-orange-300 transition-colors duration-200 p-2",onClick:()=>t("search"),"aria-label":"Open search",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})}function I({count:t}){const{open:s}=g(),{publish:i,shop:r,cart:n,prevCart:l}=H();return e.jsxs("button",{onClick:a=>{a.preventDefault(),s("cart"),i("cart_viewed",{cart:n,prevCart:l,shop:r,url:window.location.href||""})},className:"relative text-white hover:text-orange-300 transition-colors duration-200 p-2","aria-label":`Cart with ${t||0} items`,children:[e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8"})}),t!==null&&t>0&&e.jsx("span",{className:"absolute -top-1 -right-1 bg-amber-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold",children:t})]})}function pe({cart:t}){return e.jsx(u.Suspense,{fallback:e.jsx(I,{count:null}),children:e.jsx(v,{resolve:t,children:e.jsx(ge,{})})})}function ge(){const t=V(),s=U(t);return e.jsx(I,{count:(s==null?void 0:s.totalQuantity)??0})}const N={items:[{id:"gid://shopify/MenuItem/461609500728",resourceId:null,tags:[],title:"Coffee",type:"HTTP",url:"/collections/all",items:[]},{id:"gid://shopify/MenuItem/461609500729",resourceId:null,tags:[],title:"K-Cups",type:"HTTP",url:"/collections/all?section=kcups",items:[]},{id:"gid://shopify/MenuItem/461609500731",resourceId:null,tags:[],title:"Subscriptions",type:"HTTP",url:"/collections/all#section-subscriptions",items:[]},{id:"gid://shopify/MenuItem/461609500730",resourceId:null,tags:[],title:"Brew",type:"HTTP",url:"/pages/brew",items:[]},{id:"gid://shopify/MenuItem/461609599032",resourceId:"gid://shopify/Page/92591030328",tags:[],title:"Our Story",type:"PAGE",url:"/our-story",items:[]},{id:"gid://shopify/MenuItem/461609566264",resourceId:null,tags:[],title:"Contact",type:"HTTP",url:"/contact",items:[]},{id:"gid://shopify/MenuItem/461609566266",resourceId:null,tags:[],title:"Rewards",type:"HTTP",url:"/rewards",items:[]},{id:"gid://shopify/MenuItem/461609566265",resourceId:null,tags:[],title:"Affiliate",type:"HTTP",url:"/affiliate",items:[]}]},_="/search";function je({children:t,className:s="predictive-search-form",...i}){const r=k({key:"search"}),n=u.useRef(null),l=$(),a=g();function o(m){var x;m.preventDefault(),m.stopPropagation(),(x=n==null?void 0:n.current)!=null&&x.value&&n.current.blur()}function d(){var x;const m=(x=n==null?void 0:n.current)==null?void 0:x.value;l(_+(m?`?q=${m}`:"")),a.close()}function c(m){r.submit({q:m.target.value||"",limit:5,predictive:!0},{method:"GET",action:_})}return u.useEffect(()=>{var m;(m=n==null?void 0:n.current)==null||m.setAttribute("type","search")},[]),typeof t!="function"?null:e.jsx(r.Form,{...i,className:s,onSubmit:o,children:t({inputRef:n,fetcher:r,fetchResults:c,goToSearch:d})})}function f({children:t}){const s=g(),{term:i,inputRef:r,fetcher:n,total:l,items:a}=ke();function o(){r.current&&(r.current.blur(),r.current.value="")}function d(){o(),s.close()}return t({items:a,closeSearch:d,inputRef:r,state:n.state,term:i,total:l})}f.Articles=we;f.Collections=ve;f.Pages=ye;f.Products=be;f.Queries=Ne;f.Empty=_e;function we({term:t,articles:s,closeSearch:i}){return s.length?e.jsxs("div",{className:"predictive-search-result",children:[e.jsx("h5",{children:"Articles"}),e.jsx("ul",{children:s.map(r=>{var l;const n=b({baseUrl:`/blogs/${r.blog.handle}/${r.handle}`,trackingParams:r.trackingParameters,term:t.current??""});return e.jsx("li",{className:"predictive-search-result-item",children:e.jsxs(p,{onClick:i,to:n,children:[((l=r.image)==null?void 0:l.url)&&e.jsx(S,{alt:r.image.altText??"",src:r.image.url,width:50,height:50}),e.jsx("div",{children:e.jsx("span",{children:r.title})})]})},r.id)})})]},"articles"):null}function ve({term:t,collections:s,closeSearch:i}){return s.length?e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-semibold text-army-700 mb-3 uppercase tracking-wide",children:"Collections"}),e.jsx("div",{className:"space-y-2",children:s.map(r=>{var l;const n=b({baseUrl:`/collections/${r.handle}`,trackingParams:r.trackingParameters,term:t.current});return e.jsx(p,{onClick:i,to:n,className:"block p-3 bg-white rounded-lg border border-army-100 hover:border-army-300 hover:bg-army-50 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[((l=r.image)==null?void 0:l.url)&&e.jsx(S,{alt:r.image.altText??"",src:r.image.url,width:32,height:32,className:"w-8 h-8 object-cover rounded"}),e.jsx("span",{className:"text-gray-900 font-medium",children:r.title})]}),e.jsx("svg",{className:"w-4 h-4 text-army-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})},r.id)})})]},"collections"):null}function ye({term:t,pages:s,closeSearch:i}){return s.length?e.jsxs("div",{className:"predictive-search-result",children:[e.jsx("h5",{children:"Pages"}),e.jsx("ul",{children:s.map(r=>{const n=b({baseUrl:`/pages/${r.handle}`,trackingParams:r.trackingParameters,term:t.current});return e.jsx("li",{className:"predictive-search-result-item",children:e.jsx(p,{onClick:i,to:n,children:e.jsx("div",{children:e.jsx("span",{children:r.title})})})},r.id)})})]},"pages"):null}function be({term:t,products:s,closeSearch:i}){return s.length?e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-semibold text-army-700 mb-3 uppercase tracking-wide",children:"Products"}),e.jsx("div",{className:"space-y-3",children:s.map(r=>{var o,d;const n=b({baseUrl:`/products/${r.handle}`,trackingParams:r.trackingParameters,term:t.current}),l=(o=r==null?void 0:r.selectedOrFirstAvailableVariant)==null?void 0:o.price,a=(d=r==null?void 0:r.selectedOrFirstAvailableVariant)==null?void 0:d.image;return e.jsx(p,{to:n,onClick:i,className:"block p-3 bg-white rounded-lg border border-army-100 hover:border-army-300 hover:bg-army-50 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[a&&e.jsx("div",{className:"flex-shrink-0",children:e.jsx(S,{alt:a.altText??"",src:a.url,width:48,height:48,className:"w-12 h-12 object-cover rounded-lg"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:r.title}),l&&e.jsx("p",{className:"text-sm text-army-600 font-semibold",children:e.jsx(se,{data:l})})]}),e.jsx("svg",{className:"w-4 h-4 text-army-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})},r.id)})})]},"products"):null}function Ne({queries:t,queriesDatalistId:s}){return t.length?e.jsx("datalist",{id:s,children:t.map(i=>i?e.jsx("option",{value:i.text},i.text):null)}):null}function _e({term:t}){return t.current?e.jsx("div",{className:"py-12 text-center",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"w-16 h-16 bg-army-100 rounded-full flex items-center justify-center mb-4",children:e.jsx("svg",{className:"w-8 h-8 text-army-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No results found"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["No results found for ",e.jsx("q",{className:"font-medium text-army-600",children:t.current})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Try searching for coffee, gear, or check your spelling"})]})}):null}function ke(){var l,a;const t=k({key:"search"}),s=u.useRef(""),i=u.useRef(null);(t==null?void 0:t.state)==="loading"&&(s.current=String(((l=t.formData)==null?void 0:l.get("q"))||"")),u.useEffect(()=>{i.current||(i.current=document.querySelector('input[type="search"]'))},[]);const{items:r,total:n}=((a=t==null?void 0:t.data)==null?void 0:a.result)??te();return{items:r,total:n,inputRef:i,term:s,fetcher:t}}const C="bigriver_utm_params",M="bigriver_utm_expiry",Se=24;function L(t){const s={};return["utm_source","utm_medium","utm_campaign","utm_content","utm_term","utm_id"].forEach(r=>{const n=t.get(r);n&&(s[r]=n)}),s}function Ce(t){if(!(typeof window>"u"))try{if(Object.keys(t).length>0){const s=Date.now()+Se*60*60*1e3;sessionStorage.setItem(C,JSON.stringify(t)),sessionStorage.setItem(M,s.toString()),console.log("UTM parameters stored:",t)}}catch(s){console.warn("Failed to store UTM parameters:",s)}}function Me(){if(typeof window>"u")return null;try{const t=sessionStorage.getItem(C),s=sessionStorage.getItem(M);return!t||!s?null:Date.now()>parseInt(s)?(Pe(),null):JSON.parse(t)}catch(t){return console.warn("Failed to retrieve UTM parameters:",t),null}}function Pe(){if(!(typeof window>"u"))try{sessionStorage.removeItem(C),sessionStorage.removeItem(M)}catch(t){console.warn("Failed to clear UTM parameters:",t)}}function Te(t){if(t){const s=L(t);if(Object.keys(s).length>0)return Ce(s),s}return Me()||{}}function Ee(t,s){const i=new URL(t,window.location.origin);return Object.entries(s).forEach(([r,n])=>{n&&i.searchParams.set(r,n)}),i.toString()}function st(t){return{campaign_source:t.utm_source,campaign_medium:t.utm_medium,campaign_name:t.utm_campaign,campaign_content:t.utm_content,campaign_term:t.utm_term,campaign_id:t.utm_id}}function Ae({to:t,utmParams:s,children:i,className:r,onClick:n,external:l=!1}){if(l){let o=t;return typeof window<"u"&&(o=Ee(t,s)),e.jsx("a",{href:o,className:r,onClick:n,target:"_blank",rel:"noopener noreferrer",children:i})}let a=t;if(typeof window<"u"){const o=new URL(t,window.location.origin);Object.entries(s).forEach(([d,c])=>{c&&o.searchParams.set(d,c)}),a=o.pathname+o.search}return e.jsx(p,{to:a,className:r,onClick:n,children:i})}const P={PopupCTA:({children:t,className:s,to:i="/collections/all"})=>e.jsx(Ae,{to:i,utmParams:{utm_source:"website",utm_medium:"popup",utm_campaign:"promo_popup",utm_content:"shop_coffee_button"},className:s,children:t})};function Ie({isOpen:t,onClose:s}){const[i,r]=u.useState(!1),[n,l]=u.useState(!1);return u.useEffect(()=>{l(!0);const a=()=>{r(window.innerWidth<768)};return a(),window.addEventListener("resize",a),()=>window.removeEventListener("resize",a)},[]),u.useEffect(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),!t||!n?null:e.jsxs("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[e.jsx("div",{className:"absolute inset-0 backdrop-blur-sm",onClick:s}),e.jsxs("div",{className:"relative z-10 max-w-4xl mx-4 max-h-[90vh] overflow-hidden",children:[e.jsx("button",{onClick:s,className:"absolute top-4 right-4 z-20 w-10 h-10 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110","aria-label":"Close popup",children:e.jsx("svg",{className:"w-6 h-6 text-gray-800",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),e.jsxs("div",{className:"relative bg-white rounded-lg shadow-2xl overflow-hidden border-4 border-white",children:[!i&&e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:"/desktoppopup.webp",alt:"Special Offer",className:"w-full h-auto max-h-[80vh] object-contain"}),e.jsx("div",{className:"absolute bottom-6 left-6",children:e.jsxs(P.PopupCTA,{className:"inline-flex items-center px-8 py-4 bg-army-600 hover:bg-army-700 font-semibold rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl",children:[e.jsx("span",{className:"text-white",children:"Shop Coffee"}),e.jsx("svg",{className:"w-5 h-5 ml-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})})]}),i&&e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:"/mobilepopup.webp",alt:"Special Offer",className:"w-full h-auto max-h-[80vh] object-contain"}),e.jsx("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2",children:e.jsxs(P.PopupCTA,{className:"inline-flex items-center px-6 py-3 bg-army-600 hover:bg-army-700 font-semibold rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl text-sm",children:[e.jsx("span",{className:"text-white",children:"Shop Coffee"}),e.jsx("svg",{className:"w-4 h-4 ml-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})})]})]})]})]})}function Le(){const[t]=W(),[s,i]=u.useState({}),[r,n]=u.useState(!0);return u.useEffect(()=>{const l=L(t),a=Te(t);i(a),n(!1),Object.keys(l).length>0&&console.log("UTM parameters captured from URL:",l),Object.keys(a).length>0&&console.log("Active UTM parameters:",a)},[t]),{utmParams:s,isLoading:r,hasUTM:Object.keys(s).length>0}}function Oe(){const{utmParams:t,hasUTM:s}=Le();return u.useEffect(()=>{s&&typeof window<"u"&&(window.gtag&&(window.gtag("event","traffic_source_identified",{event_category:"Traffic Attribution",event_label:`${t.utm_source} / ${t.utm_medium}`,traffic_source:t.utm_source,traffic_medium:t.utm_medium,traffic_campaign:t.utm_campaign,traffic_content:t.utm_content,traffic_term:t.utm_term,custom_parameter_1:t.utm_source,custom_parameter_2:t.utm_medium}),O(t.utm_source,t.utm_medium)&&(console.log("🎯 SOCIAL MEDIA TRAFFIC DETECTED:",{platform:t.utm_source,campaign:t.utm_campaign,content:t.utm_content,timestamp:new Date().toISOString(),url:window.location.href}),window.gtag("event","social_media_traffic",{event_category:"Social Media Attribution",event_label:t.utm_source,social_platform:t.utm_source,social_campaign:t.utm_campaign,social_content:t.utm_content})),R(t.utm_medium)&&(console.log("💰 PAID ADVERTISING TRAFFIC DETECTED:",{source:t.utm_source,medium:t.utm_medium,campaign:t.utm_campaign,term:t.utm_term,timestamp:new Date().toISOString()}),window.gtag("event","paid_advertising_traffic",{event_category:"Paid Advertising Attribution",event_label:`${t.utm_source} - ${t.utm_campaign}`,ad_source:t.utm_source,ad_medium:t.utm_medium,ad_campaign:t.utm_campaign,ad_term:t.utm_term}))),console.log("📊 TRAFFIC SOURCE ATTRIBUTION:",{source:t.utm_source,medium:t.utm_medium,campaign:t.utm_campaign,content:t.utm_content,term:t.utm_term,type:Re(t.utm_source,t.utm_medium),timestamp:new Date().toISOString(),page:window.location.pathname}))},[s,t]),null}function O(t,s){if(!t&&!s)return!1;const i=["facebook","instagram","twitter","linkedin","tiktok","snapchat","pinterest","youtube","reddit"],r=["social","social-media","social_media"];return i.includes((t==null?void 0:t.toLowerCase())||"")||r.includes((s==null?void 0:s.toLowerCase())||"")}function R(t){return t?["cpc","ppc","paid","ads","advertising","paid-social","paid_social","display","banner"].includes(t.toLowerCase()):!1}function Re(t,s){return O(t,s)?"Social Media":R(s)?"Paid Advertising":s==="email"?"Email Marketing":s==="referral"?"Referral":s==="organic"?"Organic Search":"Other"}function Ue(){return u.useEffect(()=>{const t=()=>{(window.innerWidth<=768?["/headerlogo.svg","/newhomepage/mobile_homeage_bg_sf.webp","/mobilepopup.webp"]:["/headerlogo.svg","/hpheronew.svg","/newhomepage/bg_video/bg_sf_new.webp","/brewedforwild.webp"]).forEach(c=>{const m=document.createElement("link");m.rel="preload",m.as="image",m.href=c,document.head.appendChild(m)})},s=()=>{const o=document.querySelectorAll("img:not([loading])"),d=window.innerWidth<=768;o.forEach((c,m)=>{const x=d?1:3,w=c.getBoundingClientRect().top<window.innerHeight;m<x&&w?c.setAttribute("loading","eager"):c.setAttribute("loading","lazy"),c.setAttribute("decoding","async")})},i=()=>{if("IntersectionObserver"in window){const o=new IntersectionObserver(d=>{d.forEach(c=>{if(c.isIntersecting){const m=c.target;m.dataset.src&&(m.src=m.dataset.src,m.removeAttribute("data-src"),o.unobserve(m))}})});document.querySelectorAll("img[data-src]").forEach(d=>{o.observe(d)})}},r=()=>{const o=document.querySelectorAll("video"),d=window.innerWidth<=768;o.forEach(c=>{d?(c.setAttribute("preload","none"),c.paused||c.pause()):c.hasAttribute("preload")||c.setAttribute("preload","metadata"),"loading"in HTMLVideoElement.prototype&&c.setAttribute("loading","lazy"),c.hasAttribute("data-optimized")||(new IntersectionObserver(x=>{x.forEach(j=>{j.isIntersecting?c.hasAttribute("autoplay")&&c.play().catch(()=>{}):c.pause()})},{threshold:.3,rootMargin:"50px"}).observe(c),c.setAttribute("data-optimized","true"))})},n=()=>{document.querySelectorAll("script[src]:not([defer]):not([async])").forEach(d=>{const c=d.getAttribute("src");c&&!c.includes("gtag")&&!c.includes("analytics")&&d.setAttribute("defer","")})},l=()=>{const o=document.createElement("style");o.textContent=`
        @font-face {
          font-display: swap;
        }
      `,document.head.appendChild(o)},a=()=>{if(!(window.innerWidth<=768))return;const d=document.createElement("style");d.textContent=`
        @media (max-width: 768px) {
          *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }

          /* Disable expensive CSS effects on mobile */
          .transition-all,
          .transition-opacity,
          .transition-transform {
            transition: none !important;
          }
        }
      `,document.head.appendChild(d),document.body.classList.add("mobile-device")};return t(),s(),i(),r(),n(),l(),a(),()=>{}},[]),null}function He({children:t}){const s=E(),[i,r]=u.useState(!0);return u.useEffect(()=>{if(s.pathname){r(!1);const n=setTimeout(()=>r(!0),100);return()=>clearTimeout(n)}},[s.pathname]),e.jsx("div",{className:`transition-all duration-300 ${i?"opacity-100 translate-y-0":"opacity-0 translate-y-2"}`,style:{minHeight:"200px"},children:t})}function ze({cart:t,children:s=null,footer:i,header:r,isLoggedIn:n,publicStoreDomain:l}){const[o,d]=u.useState(!1);u.useEffect(()=>{},[!1]);const c=()=>{d(!1),localStorage.setItem("bigriver-promo-popup-seen","true")};return e.jsxs(y.Provider,{children:[e.jsx(Be,{cart:t}),e.jsx(Fe,{}),e.jsx(De,{header:r,publicStoreDomain:l}),r&&e.jsx(me,{header:r,cart:t,isLoggedIn:n,publicStoreDomain:l}),e.jsx("main",{children:e.jsx(He,{children:s})}),e.jsx(ce,{footer:i,header:r,publicStoreDomain:l}),e.jsx(Oe,{}),e.jsx(Ue,{}),e.jsx(Ie,{isOpen:o,onClose:c})]})}function Be({cart:t}){return e.jsx(y,{type:"cart",heading:"Shopping Cart",children:e.jsx(u.Suspense,{fallback:e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"}),e.jsx("span",{className:"ml-3 text-army-600 font-medium",children:"Loading cart..."})]}),children:e.jsx(v,{resolve:t,children:s=>e.jsx(ee,{cart:s,layout:"aside"})})})})}function Fe(){const t=u.useId();return e.jsx(y,{type:"search",heading:"SEARCH",children:e.jsxs("div",{className:"h-full flex flex-col space-y-6",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(je,{children:({fetchResults:s,goToSearch:i,inputRef:r})=>e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"relative",children:e.jsx("input",{name:"q",onChange:s,onFocus:s,placeholder:"Search for coffee, gear, or anything...",ref:r,type:"search",list:t,className:"w-full px-4 py-3 bg-white border border-army-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 text-gray-900 placeholder-gray-500"})}),e.jsx("button",{onClick:i,className:"w-full bg-army-600 text-white py-3 px-4 rounded-lg hover:bg-army-700 transition-colors duration-200 font-medium",children:"Search All Results"})]})})}),e.jsx("div",{className:"flex-1 overflow-y-auto",children:e.jsx(f,{children:({items:s,total:i,term:r,state:n,closeSearch:l})=>{const{articles:a,collections:o,pages:d,products:c,queries:m}=s;return n==="loading"&&r.current?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"}),e.jsx("span",{className:"ml-3 text-army-600 font-medium",children:"Searching..."})]}):i?e.jsxs("div",{className:"space-y-6",children:[e.jsx(f.Queries,{queries:m,queriesDatalistId:t}),e.jsx(f.Products,{products:c,closeSearch:l,term:r}),e.jsx(f.Collections,{collections:o,closeSearch:l,term:r}),e.jsx(f.Pages,{pages:d,closeSearch:l,term:r}),e.jsx(f.Articles,{articles:a,closeSearch:l,term:r}),r.current&&i?e.jsx("div",{className:"pt-4 border-t border-army-200",children:e.jsxs(p,{onClick:l,to:`${_}?q=${r.current}`,className:"block w-full p-4 bg-army-600 text-white rounded-lg hover:bg-army-700 transition-colors duration-200 text-center font-medium",children:["View all ",i,' results for "',r.current,'"']})}):null]}):e.jsx(f.Empty,{term:r})}})})]})})}function De({header:t,publicStoreDomain:s}){var i;return t.menu&&((i=t.shop.primaryDomain)==null?void 0:i.url)&&e.jsx(y,{type:"mobile",heading:"MENU",children:e.jsx(A,{menu:t.menu,viewport:"mobile",primaryDomainUrl:t.shop.primaryDomain.url,publicStoreDomain:s})})}const Ve=u.lazy(()=>T(()=>import("./GoogleAnalytics-jquwe-Bo.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11])).then(t=>({default:t.GoogleAnalytics}))),$e=u.lazy(()=>T(()=>import("./GTMLoader-BNOsfSYk.js"),__vite__mapDeps([12,2])).then(t=>({default:t.GTMLoader})));function We(){const[t,s]=u.useState(!1);return u.useEffect(()=>{u.startTransition(()=>{s(!0)})},[]),t?e.jsxs(u.Suspense,{fallback:null,children:[e.jsx($e,{}),e.jsx(Ve,{})]}):null}const rt=({formMethod:t,currentUrl:s,nextUrl:i})=>!!(t&&t!=="GET"||s.toString()===i.toString());function it(){return[{rel:"preconnect",href:"https://cdn.shopify.com"},{rel:"preconnect",href:"https://shop.app"},{rel:"preconnect",href:"https://www.googletagmanager.com"},{rel:"preconnect",href:"https://www.google-analytics.com"},{rel:"preconnect",href:"https://fonts.googleapis.com"},{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"},{rel:"stylesheet",href:"https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Oswald:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700;800&display=swap"},{rel:"preload",href:"/headerlogo.svg",as:"image",type:"image/svg+xml"},{rel:"preload",href:"/hpheronew.svg",as:"image",type:"image/svg+xml"},{rel:"icon",type:"image/svg+xml",href:re}]}function nt({children:t}){const s=z(),i=Y("root"),r=E(),n=r.pathname==="/"||r.pathname==="";return e.jsxs("html",{lang:"en",children:[e.jsxs("head",{children:[e.jsx("meta",{charSet:"utf-8"}),e.jsx("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),e.jsx("meta",{name:"theme-color",content:"#4a5d23"}),e.jsx("meta",{name:"format-detection",content:"telephone=no"}),e.jsx("meta",{httpEquiv:"x-dns-prefetch-control",content:"on"}),e.jsx("link",{rel:"dns-prefetch",href:"//cdn.shopify.com"}),e.jsx("link",{rel:"dns-prefetch",href:"//www.googletagmanager.com"}),e.jsx("link",{rel:"dns-prefetch",href:"//www.google-analytics.com"}),e.jsx("link",{rel:"preload",href:"/headerlogo.svg",as:"image",type:"image/svg+xml"}),e.jsx("link",{rel:"preload",href:"/newhomepage/shop_stillframe.webp",as:"image",type:"image/webp"}),e.jsx("link",{rel:"preload",href:"/newhomepage/mobile_homeage_bg_sf.webp",as:"image",type:"image/webp"}),e.jsx("link",{rel:"preconnect",href:"https://cdn.shopify.com",crossOrigin:"anonymous"}),e.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com",crossOrigin:"anonymous"}),e.jsx("link",{rel:"icon",type:"image/x-icon",href:"/Logo_Official.ico?v=1"}),e.jsx("link",{rel:"shortcut icon",type:"image/x-icon",href:"/Logo_Official.ico?v=1"}),e.jsx("link",{rel:"apple-touch-icon",href:"/Logo_Official.ico?v=1"}),e.jsx("link",{rel:"stylesheet",href:ie}),e.jsx("link",{rel:"stylesheet",href:ne}),e.jsx("link",{rel:"stylesheet",href:oe}),e.jsx("link",{rel:"stylesheet",href:ae}),e.jsx(q,{}),e.jsx(G,{})]}),e.jsxs("body",{className:n?"homepage":"",children:[i?e.jsxs(B.Provider,{cart:i.cart,shop:i.shop,consent:i.consent,children:[e.jsx(We,{}),e.jsx(ze,{...i,children:t})]}):t,e.jsx(K,{nonce:s}),e.jsx(J,{nonce:s})]})]})}const ot=F(function(){return e.jsx(Q,{})}),at=D(function(){var n;const s=X();let i="Unknown error",r=500;return Z(s)?(i=((n=s==null?void 0:s.data)==null?void 0:n.message)??s.data,r=s.status):s instanceof Error&&(i=s.message),e.jsxs("div",{className:"route-error",children:[e.jsx("h1",{children:"Oops"}),e.jsx("h2",{children:r}),i&&e.jsx("fieldset",{children:e.jsx("pre",{children:i})})]})});export{at as E,nt as L,st as f,it as l,ot as r,rt as s,Le as u};
