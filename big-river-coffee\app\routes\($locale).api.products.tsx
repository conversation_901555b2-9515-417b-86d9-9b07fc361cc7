import {type LoaderFunctionArgs, json} from '@shopify/remix-oxygen';

/**
 * API route for progressive product loading
 * Used by ProgressiveProductLoader for infinite scroll and load more functionality
 */
export async function loader({request, context}: LoaderFunctionArgs) {
  const {storefront} = context;
  const url = new URL(request.url);
  
  // Get query parameters
  const cursor = url.searchParams.get('cursor');
  const query = url.searchParams.get('query') || 'available_for_sale:true';
  const pageBy = parseInt(url.searchParams.get('pageBy') || '8', 10);
  
  // Validate parameters
  if (!cursor) {
    return json({error: 'Cursor parameter is required'}, {status: 400});
  }
  
  if (pageBy > 50) {
    return json({error: 'Page size too large (max 50)'}, {status: 400});
  }
  
  try {
    // Use minimal query for fast loading
    const result = await storefront.query(PRODUCTS_API_QUERY, {
      variables: {
        first: pageBy,
        after: cursor,
        query: query,
      },
      cache: storefront.CacheLong(), // Aggressive caching for API responses
    });
    
    return json({
      products: result.products,
      success: true,
    });
    
  } catch (error) {
    console.error('Error loading more products:', error);
    return json({
      error: 'Failed to load products',
      success: false,
    }, {status: 500});
  }
}

// Minimal GraphQL query for API responses - only essential fields
const PRODUCTS_API_QUERY = `#graphql
  query ProductsAPI(
    $country: CountryCode
    $language: LanguageCode
    $first: Int
    $after: String
    $query: String
  ) @inContext(country: $country, language: $language) {
    products(first: $first, after: $after, query: $query) {
      nodes {
        id
        title
        handle
        availableForSale
        priceRange {
          minVariantPrice {
            amount
            currencyCode
          }
        }
        featuredImage {
          id
          altText
          url
          width
          height
        }
        tags
        variants(first: 1) {
          nodes {
            id
            title
            availableForSale
            selectedOptions {
              name
              value
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
` as const;
