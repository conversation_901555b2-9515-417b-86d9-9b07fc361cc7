import{w as t}from"./with-props-CE_bzIRz.js";import{j as s}from"./jsx-runtime-CWqDQG74.js";import{u as n,L as r}from"./chunk-D4RADZKF-CZTShXQu.js";import{P as i}from"./PaginatedResourceSection-ClJsfpf4.js";import"./index-7gSH2QeO.js";const g=()=>[{title:"Hydrogen | Blogs"}],p=t(function(){const{blogs:o}=n();return s.jsxs("div",{className:"blogs",children:[s.jsx("h1",{children:"Blogs"}),s.jsx("div",{className:"blogs-grid",children:s.jsx(i,{connection:o,children:({node:e})=>s.jsx(r,{className:"blog",prefetch:"intent",to:`/blogs/${e.handle}`,children:s.jsx("h2",{children:e.title})},e.handle)})})]})});export{p as default,g as meta};
